# !pip install pypdf2  pdf解析
# !pip install dashscope 大模型
# !pip install langchain 语言模型
# !pip install langchain-openai 语言模型
# !pip install langchain-community 语言模型
# !pip install faiss-cpu 向量检索

import os
import logging
import pickle
from PyPDF2 import PdfReader
from langchain.chains.question_answering import load_qa_chain
from langchain_openai import OpenAI, ChatOpenAI
from langchain_openai import OpenAIEmbeddings
from langchain_community.embeddings import DashScopeEmbeddings
from langchain_community.callbacks.manager import get_openai_callback
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from typing import List, Tuple

os.environ["DASHSCOPE_API_KEY"] = "sk-04e1dcd356c74e34bf3c3fcc939c5cc2"
os.environ["DASHSCOPE_PROXY"] = "https://dashscope.aliyuncs.com/compatible-mode/v1"

# 提取文本并记录页码
def extract_text_with_page_numbers(pdf) -> Tuple[str, List[int]]:
    """
    从PDF文件中提取文本内容，并记录每行文本对应的页码

    参数:
        pdf: PDF文件对象，通常是通过PdfReader读取的PDF文件

    返回:
        Tuple[str, List[int]]: 返回一个元组，包含提取的全部文本和对应的页码列表
    """

    # 初始化空字符串，用于存储所有提取的文本内容
    text = ""

    # 初始化空列表，用于存储每行文本对应的页码
    page_numbers = []

    # 遍历PDF的每一页，enumerate函数用于同时获取页码和页面对象
    # start=1表示页码从1开始计数（而不是从0开始）
    for page_number, page in enumerate(pdf.pages, start=1):

        # 从当前页面提取文本内容
        extracted_text = page.extract_text()

        # 检查是否成功提取到文本（不为空）
        if extracted_text:
            # 将当前页的文本添加到总文本中
            text += extracted_text

            # 将当前页的文本按行分割，计算行数
            # 然后为每一行都记录对应的页码
            # [page_number]*len(...) 创建一个包含多个相同页码的列表
            page_numbers.extend([page_number] * len(extracted_text.split("\n")))
        else:
            # 如果当前页没有提取到文本，记录警告信息
            logging.warning(f"Page {page_number} is empty")

    return text, page_numbers

def process_text_with_splitter(
    text: str, page_numbers: List[int], save_path: str = None
) -> FAISS:
    """
    处理文本并构建向量知识库
    
    这个函数的主要作用是：
    1. 将长文本分割成小块（便于AI理解和检索）
    2. 将文本块转换为向量（数字表示，便于计算相似度）
    3. 构建FAISS向量数据库（用于快速相似度搜索）
    4. 保存知识库到本地文件
    
    参数:
        text (str): 需要处理的完整文本内容
        page_numbers (List[int]): 每行文本对应的页码列表
        save_path (str, optional): 保存知识库的文件夹路径，如果不提供则不保存
    
    返回:
        FAISS: 构建好的向量知识库对象
    """
    
    # 创建文本分割器 - 用于将长文本切分成小块
    # 这样做的原因：AI模型处理短文本效果更好，长文本容易丢失重要信息
    text_splitter = RecursiveCharacterTextSplitter(
        chunk_size=512,        # 每个文本块的最大字符数（约等于一段话的长度）
        chunk_overlap=128,     # 相邻文本块之间的重叠字符数（避免重要信息被截断）
        length_function=len,   # 用于计算文本长度的函数
        separators=["\n\n", "\n", " ", "", "."],  # 分割优先级：段落 > 行 > 空格 > 句号
    )
    
    # 执行文本分割
    chunks = text_splitter.split_text(text)
    print(f"文本被分割成了{len(chunks)}个片段")
    
    # 创建文本嵌入模型 - 用于将文本转换为向量（数字表示）
    # 向量可以用来计算文本之间的相似度
    embeddings = DashScopeEmbeddings(model="text-embedding-v4",dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"))
    
    # 使用FAISS构建向量知识库
    # FAISS是Facebook开发的高效相似度搜索库
    # from_texts方法会自动将所有文本块转换为向量并建立索引
    knodedgeBase = FAISS.from_texts(chunks, embeddings)
    print(f"知识库构建完成，向量数量：{len(knodedgeBase.index_to_docstore_id)}")
    
    # 创建页码信息映射 - 记录每个文本块来自哪一页
    # 这样在检索时可以告诉用户答案来源于PDF的第几页
    page_info = {chunk: page_numbers[i] for i, chunk in enumerate(chunks)}
    
    # 将页码信息附加到知识库对象上（自定义属性）
    knodedgeBase.page_info = page_info
    
    # 如果用户指定了保存路径，则将知识库保存到本地
    if save_path:
        # 创建保存目录（如果不存在的话）
        os.makedirs(save_path, exist_ok=True)
        
        # 保存FAISS向量数据库到本地文件
        # 这样下次可以直接加载，不用重新构建
        knodedgeBase.save_local(save_path)
        print(f"知识库已保存到{save_path}")
        
        # 单独保存页码信息到pickle文件
        # pickle是Python的序列化格式，可以保存Python对象
        with open(os.path.join(save_path, "page_info.pkl"), "wb") as f:
            pickle.dump(page_info, f)
    
    return knodedgeBase

def load_knowledge_base(load_path:str,embeddings = None)->FAISS:
    """加载已保存的知识库"""
    if embeddings is None:
        embeddings = DashScopeEmbeddings(model="text-embedding-v4",dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"))

    knodedgeBase = FAISS.load_local(load_path,embeddings,allow_dangerous_deserialization=True)
    print(f"知识库加载完成，向量数量：{len(knodedgeBase.load_path)}")

    page_info_path = os.path.join(load_path,"page_info.pkl")
    if os.path.exists(page_info_path):
        with open(page_info_path,"rb") as f:
            page_info = pickle.load(f)
            knodedgeBase.page_info = page_info
            print(f"页码信息加载完成，共{len(page_info)}个片段")
    else:
        print("未找到页码信息，无法提供页码信息")

    return knodedgeBase

# 读取PDF文件
pdf_reader = PdfReader('./浦发上海浦东发展银行西安分行个金客户经理考核办法.pdf')
print("pdf_reader",pdf_reader)
# 提取文本和页码信息
text, page_numbers = extract_text_with_page_numbers(pdf_reader)
print("text",text)
print("page_numbers",page_numbers)

save_dir = "./vector_db"
knowledge_base = process_text_with_splitter(text,page_numbers,save_dir)

knowledge_base

from langchain.prompts import PromptTemplate
from langchain.schema import Document
# query = "客户经理被投诉一次扣多少分"
query = "客户经理每年评聘申报时间是怎样的"
# query = "资格考试在几月份"
load_dir = "./vector_db"
prompt_template = """
你是一个专业的知识库助手，请根据以下信息回答问题：

{context}

问题：{question}

回答：
"""



docs = knowledge_base.similarity_search(query)
print("docs", docs)
prompt= PromptTemplate(template=prompt_template,input_variables=[docs,query])
formatted_prompt = prompt.format(
    subject="数据分析",  # 根据具体需求修改
    context=docs,  # 添加相关背景
    question=query # 用户的具体问题
)
documents = [
    Document(
        page_content=formatted_prompt,
        metadata={"source": "user_prompt"}
    )
]


if query:
   
    chatLLM = ChatOpenAI(
        model="deepseek-v3", 
        temperature=0, 
        api_key=os.getenv("DASHSCOPE_API_KEY"),
        base_url=os.getenv("DASHSCOPE_PROXY")
    )
    chain = load_qa_chain(chatLLM,chain_type="stuff")
    input_data = {"input_documents":documents,"question":query}

    with get_openai_callback() as cost:
        response = chain.invoke(input_data)
        print(f"查询已处理。成本:{cost}")
        print(f"响应: {response['output_text']}")

    # unique_pages = set()

    # for doc in docs:
    #     text_content = getattr(doc,"page_content","")
    #     source_page = knowledge_base.page_info.get(text_content.strip(),"未知")

    #     if source_page not in unique_pages:
    #         unique_pages.add(source_page)
    #         print(f"文本块页码{source_page}:{text_content}")



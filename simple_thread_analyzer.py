#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
线程快照分析工具
用于分析特定线程的状态和堆栈信息
"""

import re
import sys
from typing import List, Dict, Optional


class ThreadAnalyzer:
    """线程分析器"""

    def __init__(self, thread_dump_content: str):
        """
        初始化线程分析器

        Args:
            thread_dump_content: 线程快照内容
        """
        self.content = thread_dump_content
        self.threads = self._parse_threads()

    def _parse_threads(self) -> List[Dict]:
        """
        解析线程快照，提取所有线程信息

        Returns:
            List[Dict]: 线程信息列表
        """
        threads = []
        lines = self.content.split("\n")
        current_thread = None

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 匹配线程头部信息
            thread_match = re.match(r'"([^"]+)"\s+#(\d+)(.+)', line)
            if thread_match:
                if current_thread:
                    threads.append(current_thread)

                current_thread = {
                    "name": thread_match.group(1),
                    "id": thread_match.group(2),
                    "info": thread_match.group(3).strip(),
                    "state": "",
                    "stack_trace": [],
                    "full_info": [line],
                }
            elif current_thread and line.startswith("java.lang.Thread.State:"):
                current_thread["state"] = line.replace(
                    "java.lang.Thread.State:", ""
                ).strip()
                current_thread["full_info"].append(line)
            elif current_thread and (
                line.startswith("\tat ") or line.startswith("\t- ")
            ):
                current_thread["stack_trace"].append(line)
                current_thread["full_info"].append(line)
            elif current_thread and line:
                current_thread["full_info"].append(line)

            i += 1

        if current_thread:
            threads.append(current_thread)

        return threads

    def find_thread_by_name(self, thread_name: str) -> Optional[Dict]:
        """
        根据线程名称查找线程

        Args:
            thread_name: 线程名称（支持模糊匹配）

        Returns:
            Optional[Dict]: 线程信息，如果未找到返回None
        """
        for thread in self.threads:
            if thread_name.lower() in thread["name"].lower():
                return thread
        return None

    def analyze_specific_thread(self, thread_name: str) -> str:
        """
        分析特定线程的详细信息

        Args:
            thread_name: 线程名称

        Returns:
            str: 分析结果
        """
        thread = self.find_thread_by_name(thread_name)
        if not thread:
            return f"❌ 未找到名称包含 '{thread_name}' 的线程"

        analysis = []
        analysis.append("🔍 线程分析报告")
        analysis.append("=" * 50)
        analysis.append(f"📝 线程名称: {thread['name']}")
        analysis.append(f"🆔 线程ID: #{thread['id']}")
        analysis.append(f"📊 线程状态: {thread['state']}")
        analysis.append(f"ℹ️  系统信息: {thread['info']}")
        analysis.append("")

        # 分析线程状态
        state_analysis = self._analyze_thread_state(thread)
        analysis.append("📈 状态分析:")
        analysis.extend([f"   {line}" for line in state_analysis])
        analysis.append("")

        # 分析堆栈信息
        if thread["stack_trace"]:
            analysis.append("📚 堆栈跟踪:")
            for i, stack_line in enumerate(thread["stack_trace"][:10]):  # 只显示前10行
                analysis.append(f"   {i+1:2d}. {stack_line.strip()}")

            if len(thread["stack_trace"]) > 10:
                analysis.append(f"   ... 还有 {len(thread['stack_trace']) - 10} 行")

            analysis.append("")

            # 堆栈关键信息分析
            stack_analysis = self._analyze_stack_trace(thread["stack_trace"])
            analysis.append("🔎 堆栈关键信息:")
            analysis.extend([f"   {line}" for line in stack_analysis])

        analysis.append("")
        analysis.append("📋 完整线程信息:")
        analysis.extend([f"   {line}" for line in thread["full_info"]])

        return "\n".join(analysis)

    def _analyze_thread_state(self, thread: Dict) -> List[str]:
        """
        分析线程状态

        Args:
            thread: 线程信息

        Returns:
            List[str]: 状态分析结果
        """
        state = thread["state"]
        analysis = []

        if "RUNNABLE" in state:
            analysis.append("✅ 线程正在运行或可运行状态")
            analysis.append("   - 线程当前正在执行或等待CPU调度")
            analysis.append("   - 这是正常的活跃状态")
        elif "WAITING" in state:
            analysis.append("⏸️  线程处于等待状态")
            if "parking" in state:
                analysis.append("   - 线程被park，等待某个条件满足")
            analysis.append("   - 可能在等待锁、条件变量或其他同步原语")
        elif "TIMED_WAITING" in state:
            analysis.append("⏰ 线程处于限时等待状态")
            analysis.append("   - 线程在指定时间内等待某个条件")
        elif "BLOCKED" in state:
            analysis.append("🚫 线程被阻塞")
            analysis.append("   - 线程等待获取锁或监视器")
        else:
            analysis.append(f"❓ 未知状态: {state}")

        return analysis

    def _analyze_stack_trace(self, stack_trace: List[str]) -> List[str]:
        """
        分析堆栈跟踪信息

        Args:
            stack_trace: 堆栈跟踪列表

        Returns:
            List[str]: 堆栈分析结果
        """
        analysis = []

        # 查找关键方法和类
        key_patterns = {
            "kafka": r"(kafka|apache\.kafka)",
            "rocketmq": r"(rocketmq|apache\.rocketmq)",
            "thread_pool": r"(ThreadPool|ExecutorService)",
            "network": r"(nio|netty|socket)",
            "consumer": r"(consumer|consume)",
            "message": r"(message|msg)",
            "listener": r"listener",
            "lock": r"(lock|synchronized|park)",
            "queue": r"(queue|blocking)",
        }

        found_patterns = set()
        for stack_line in stack_trace:
            for pattern_name, pattern in key_patterns.items():
                if re.search(pattern, stack_line, re.IGNORECASE):
                    found_patterns.add(pattern_name)

        if "consumer" in found_patterns or "message" in found_patterns:
            analysis.append("📨 这是一个消息消费线程")

        if "kafka" in found_patterns:
            analysis.append("🔄 使用Kafka消息队列")
        elif "rocketmq" in found_patterns:
            analysis.append("🚀 使用RocketMQ消息队列")

        if "listener" in found_patterns:
            analysis.append("👂 实现了消息监听器模式")

        if "network" in found_patterns:
            analysis.append("🌐 涉及网络I/O操作")

        if "lock" in found_patterns:
            analysis.append("🔒 涉及锁或同步操作")

        if "queue" in found_patterns:
            analysis.append("📦 使用队列进行数据处理")

        if not analysis:
            analysis.append("📝 常规业务逻辑执行")

        return analysis


def main():
    """主函数"""
    # 这里应该读取线程快照文件
    # 由于当前没有具体的文件内容，我们提供一个分析模板

    print("🔍 ConsumeMessageThread_user-ext-organization-listener_1 线程分析")
    print("=" * 60)
    print()

    # 基于线程名称的初步分析
    print("📝 基于线程名称的分析:")
    print("   1️⃣  这是一个消息消费线程 (ConsumeMessageThread)")
    print("   2️⃣  处理用户扩展组织相关消息 (user-ext-organization)")
    print("   3️⃣  使用监听器模式 (listener)")
    print("   4️⃣  线程序号为1，可能是消费者组中的第一个线程")
    print()

    print("🎯 可能的功能:")
    print("   • 处理用户组织架构变更消息")
    print("   • 监听组织相关的事件通知")
    print("   • 执行用户扩展功能的组织业务逻辑")
    print()

    print("⚠️  需要关注的问题:")
    print("   • 如果线程长时间处于RUNNABLE状态，可能存在CPU密集型操作")
    print("   • 如果线程处于WAITING状态，可能在等待消息或锁")
    print("   • 如果线程BLOCKED，可能存在锁竞争")
    print("   • 检查是否有死锁或资源竞争")
    print()

    print("🔧 建议的排查方向:")
    print("   1. 检查消息队列的消费情况")
    print("   2. 查看组织架构相关的业务逻辑")
    print("   3. 排查数据库连接和事务")
    print("   4. 检查网络连接状态")
    print("   5. 分析内存使用情况")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ConsumeMessageThread_user-ext-organization-listener_1线程分析
"""

from simple_thread_analyzer import ThreadA<PERSON>yzer


def test_consume_message_thread_analysis():
    """测试消息消费线程分析功能"""

    # 模拟ConsumeMessageThread_user-ext-organization-listener_1的线程快照数据
    sample_thread_dump = """
"ConsumeMessageThread_user-ext-organization-listener_1" #4337600 prio=5 os_prio=0 tid=0x00007f6a64f17000 nid=0x19aee runnable [0x00007f69364e1000]
   java.lang.Thread.State: RUNNABLE
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at java.io.BufferedInputStream.fill(BufferedInputStream.java:246)
	at java.io.BufferedInputStream.read1(BufferedInputStream.java:286)
	at java.io.BufferedInputStream.read(BufferedInputStream.java:345)
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at org.apache.rocketmq.remoting.netty.NettyRemotingClient$ChannelWrapper.isOK(NettyRemotingClient.java:685)
	at org.apache.rocketmq.remoting.netty.NettyRemotingClient.getAndCreateChannel(NettyRemotingClient.java:465)
	at org.apache.rocketmq.remoting.netty.NettyRemotingClient.invokeSync(NettyRemotingClient.java:377)
	at org.apache.rocketmq.client.impl.MQClientAPIImpl.pullMessage(MQClientAPIImpl.java:1184)
	at org.apache.rocketmq.client.impl.consumer.PullAPIWrapper.pullKernelImpl(PullAPIWrapper.java:229)
	at org.apache.rocketmq.client.impl.consumer.PullAPIWrapper.pullKernelImpl(PullAPIWrapper.java:240)
	at org.apache.rocketmq.client.impl.consumer.DefaultMQPushConsumerImpl.pullMessage(DefaultMQPushConsumerImpl.java:393)
	at org.apache.rocketmq.client.impl.consumer.ConsumeMessageConcurrentlyService$ConsumeRequest.run(ConsumeMessageConcurrentlyService.java:367)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run(FutureTask.java:266)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:855)

"ConsumeMessageThread_user-ext-organization-listener_2" #4337601 prio=5 os_prio=0 tid=0x00007f6a64f18000 nid=0x19aef waiting on condition [0x00007f69364e2000]
   java.lang.Thread.State: WAITING (parking)
	at sun.misc.Unsafe.park0(Native Method)
	- parking to wait for  <0x00000000f2345678> (a java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject)
	at sun.misc.Unsafe.park(Unsafe.java:1038)
	at java.util.concurrent.locks.LockSupport.park(LockSupport.java:176)
	at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2047)
"""

    print("🧪 测试ConsumeMessageThread_user-ext-organization-listener_1线程分析")
    print("=" * 70)
    print()

    # 创建分析器
    analyzer = ThreadAnalyzer(sample_thread_dump)

    # 分析目标线程
    target_thread_name = "ConsumeMessageThread_user-ext-organization-listener_1"
    analysis_result = analyzer.analyze_specific_thread(target_thread_name)

    print(analysis_result)

    print("\n" + "=" * 70)
    print("🔍 详细诊断建议:")
    print("=" * 70)

    print(
        """
📊 当前线程状态分析:
   ✅ 线程状态: RUNNABLE - 线程正在执行网络I/O操作
   📡 网络操作: 正在从Socket读取数据 (socketRead0)
   🚀 消息队列: 使用RocketMQ进行消息拉取
   
🔧 具体问题分析:
   1️⃣  网络I/O: 线程正在等待网络数据，这是正常的阻塞I/O操作
   2️⃣  消息拉取: 正在从RocketMQ服务器拉取消息
   3️⃣  连接状态: 检查与RocketMQ Broker的网络连接
   
⚠️  可能的性能问题:
   • 网络延迟: 如果网络响应慢，会导致线程长时间阻塞在socketRead
   • 消息堆积: 可能存在消息消费速度跟不上生产速度
   • 连接池: 检查RocketMQ客户端的连接池配置
   
🛠️  优化建议:
   1. 监控网络延迟和带宽
   2. 检查RocketMQ Broker的负载情况
   3. 调整消费者线程数量
   4. 优化消息处理业务逻辑
   5. 考虑使用异步消费模式
   
📈 监控指标:
   • 消息消费速率 (TPS)
   • 消息积压数量
   • 网络连接状态
   • 线程池使用情况
   • GC频率和耗时
"""
    )


def demonstrate_thread_states():
    """演示不同线程状态的分析"""

    print("\n🎯 线程状态示例分析")
    print("=" * 50)

    thread_states = {
        "RUNNABLE (网络I/O)": {
            "description": "线程正在执行网络读取操作",
            "common_causes": ["等待网络数据包", "Socket连接延迟", "网络带宽限制"],
            "solutions": ["检查网络连接质量", "优化网络配置", "使用连接池"],
        },
        "WAITING (parking)": {
            "description": "线程被暂停，等待条件满足",
            "common_causes": [
                "等待消息队列中的新消息",
                "等待其他线程释放锁",
                "等待定时任务触发",
            ],
            "solutions": ["检查消息生产者状态", "排查锁竞争问题", "调整等待超时时间"],
        },
        "BLOCKED": {
            "description": "线程被阻塞，无法获取锁",
            "common_causes": ["多线程竞争同一个锁", "死锁情况", "锁持有时间过长"],
            "solutions": ["减少锁的粒度", "使用读写锁", "检查死锁情况"],
        },
    }

    for state, info in thread_states.items():
        print(f"\n🔸 {state}:")
        print(f"   📝 描述: {info['description']}")
        print(f"   🔍 常见原因:")
        for cause in info["common_causes"]:
            print(f"      • {cause}")
        print(f"   💡 解决方案:")
        for solution in info["solutions"]:
            print(f"      • {solution}")


if __name__ == "__main__":
    test_consume_message_thread_analysis()
    demonstrate_thread_states()
